package com.myfitnessjourney.backend_service.repository;

import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FoodNutrientsRepository extends JpaRepository<FoodNutrients, Long> {

    @Query(value = """
        SELECT * FROM food_nutrients
        WHERE search_vector @@ plainto_tsquery('english', :query)
        ORDER BY ts_rank_cd(search_vector, plainto_tsquery('english', :query)) DESC
        LIMIT 20
        """, nativeQuery = true)
    List<FoodNutrients> searchFoodByText(@Param("query") String query);
}
