name: Test Deployment (Dry Run)

on:
  workflow_dispatch:  # Manual trigger only
  pull_request:
    paths:
      - '.github/workflows/deploy.yml'
      - 'pom.xml'
      - 'src/**'

env:
  AWS_REGION: ap-south-1
  S3_BUCKET: elasticbeanstalk-ap-south-1-${{ secrets.AWS_ACCOUNT_ID }}
  APP_NAME: diet-app
  ENV_NAME: Diet-app-env-1

jobs:
  validate-build:
    name: Validate Build Process
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Build with <PERSON><PERSON> (Test)
        run: ./mvnw clean verify package --file pom.xml

      - name: Check JAR file exists
        run: |
          JAR_FILE="target/diet-app-backend-0.0.1-SNAPSHOT.jar"
          if [ -f "$JAR_FILE" ]; then
            echo "✅ JAR file created successfully: $JAR_FILE"
            ls -lh "$JAR_FILE"
          else
            echo "❌ JAR file not found: $JAR_FILE"
            echo "Available files in target/:"
            ls -la target/ || echo "target/ directory not found"
            exit 1
          fi

      - name: Generate test version label
        id: version
        run: |
          VERSION_LABEL="test-${{ github.sha }}-$(date +%Y%m%d-%H%M%S)"
          echo "version-label=$VERSION_LABEL" >> $GITHUB_OUTPUT
          echo "Generated test version label: $VERSION_LABEL"

      - name: Simulate S3 upload (dry run)
        run: |
          JAR_FILE="target/diet-app-backend-0.0.1-SNAPSHOT.jar"
          S3_KEY="${{ env.APP_NAME }}/${{ steps.version.outputs.version-label }}.jar"
          
          echo "🧪 DRY RUN: Would upload JAR to S3"
          echo "  Source: $JAR_FILE"
          echo "  Destination: s3://${{ env.S3_BUCKET }}/$S3_KEY"
          echo "  File size: $(du -h "$JAR_FILE" | cut -f1)"

  validate-aws-config:
    name: Validate AWS Configuration
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'  # Only run on manual trigger
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Test AWS connectivity
        run: |
          echo "🔍 Testing AWS connectivity..."
          aws sts get-caller-identity
          
          echo "📦 Checking S3 bucket access..."
          aws s3 ls "s3://${{ env.S3_BUCKET }}/" || echo "⚠️ S3 bucket not accessible or doesn't exist"
          
          echo "🌱 Checking Elastic Beanstalk application..."
          aws elasticbeanstalk describe-applications --application-names ${{ env.APP_NAME }} || echo "⚠️ EB application not found"
          
          echo "🌍 Checking Elastic Beanstalk environment..."
          aws elasticbeanstalk describe-environments --application-name ${{ env.APP_NAME }} --environment-names ${{ env.ENV_NAME }} || echo "⚠️ EB environment not found"
